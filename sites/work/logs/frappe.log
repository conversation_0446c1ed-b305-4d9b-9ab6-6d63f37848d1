2025-07-01 13:30:47,075 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 101, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 275, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1389, in setup_module_map
    apps = get_installed_apps(_ensure_on_bench=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 189, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory
2025-07-01 13:30:47,086 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 101, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 275, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1389, in setup_module_map
    apps = get_installed_apps(_ensure_on_bench=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 189, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory
2025-07-01 13:30:48,139 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 101, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 275, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1389, in setup_module_map
    apps = get_installed_apps(_ensure_on_bench=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 189, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory
2025-07-01 13:30:48,143 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 101, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 275, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1389, in setup_module_map
    apps = get_installed_apps(_ensure_on_bench=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 189, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory
2025-07-01 13:30:48,343 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 101, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 275, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1389, in setup_module_map
    apps = get_installed_apps(_ensure_on_bench=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 189, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory
2025-07-01 13:30:48,347 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 101, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 275, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1389, in setup_module_map
    apps = get_installed_apps(_ensure_on_bench=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 189, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory
2025-07-01 13:30:48,579 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 101, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 275, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1389, in setup_module_map
    apps = get_installed_apps(_ensure_on_bench=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 189, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory
2025-07-01 13:30:48,581 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 101, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 275, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1389, in setup_module_map
    apps = get_installed_apps(_ensure_on_bench=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 189, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory
2025-07-01 13:30:48,785 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 101, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 275, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1389, in setup_module_map
    apps = get_installed_apps(_ensure_on_bench=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 189, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory
2025-07-01 13:30:48,796 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 101, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 275, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1389, in setup_module_map
    apps = get_installed_apps(_ensure_on_bench=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 189, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory
2025-07-01 13:30:50,975 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 101, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 275, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1389, in setup_module_map
    apps = get_installed_apps(_ensure_on_bench=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 189, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory
2025-07-01 13:30:50,985 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 101, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 275, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1389, in setup_module_map
    apps = get_installed_apps(_ensure_on_bench=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 189, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory
2025-07-01 16:29:20,492 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'name': 'Home', 'public': '1', 'new_widgets': '{}', 'blocks': '[{"id":"kb3XPLg8lb","type":"header","data":{"text":"<span class=\\"h4\\"><b>Your Shortcuts</b></span>","col":12}},{"id":"nWd2KJPW8l","type":"shortcut","data":{"shortcut_name":"Item","col":3}},{"id":"snrzfbFr5Y","type":"shortcut","data":{"shortcut_name":"Customer","col":3}},{"id":"SHJKakmLLf","type":"shortcut","data":{"shortcut_name":"Supplier","col":3}},{"id":"CPxEyhaf3G","type":"shortcut","data":{"shortcut_name":"Sales Invoice","col":3}},{"id":"WU4F-HUcIQ","type":"shortcut","data":{"shortcut_name":"Leaderboard","col":3}},{"id":"d_KVM1gsf9","type":"spacer","data":{"col":12}},{"id":"JVu8-FJZCu","type":"header","data":{"text":"<span class=\\"h4\\"><b>Reports &amp; Masters</b></span>","col":12}},{"id":"JiuSi0ubOg","type":"card","data":{"card_name":"Accounting","col":4}},{"id":"ji2Jlm3Q8i","type":"card","data":{"card_name":"Stock","col":4}},{"id":"N61oiXpuwK","type":"card","data":{"card_name":"CRM","col":4}},{"id":"6J0CVl1mPo","type":"card","data":{"card_name":"Data Import and Settings","col":4}}]', 'cmd': 'frappe.desk.doctype.workspace.workspace.save_page'}
