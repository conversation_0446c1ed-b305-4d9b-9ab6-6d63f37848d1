2025-07-01 15:28:00,622 ERROR scheduler Exception in Enqueue Events for Site work
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 1536, in get_connection
    if connection.can_read() and self.cache is None:
       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 622, in can_read
    return self._parser.can_read(timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/_parsers/hiredis.py", line 99, in can_read
    return self.read_from_socket(timeout=timeout, raise_on_timeout=False)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/_parsers/hiredis.py", line 110, in read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
redis.exceptions.ConnectionError: Connection closed by server.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 385, in connect_check_health
    sock = self.retry.call_with_retry(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/retry.py", line 87, in call_with_retry
    return do()
           ^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 386, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
            ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 797, in _connect
    raise err
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 781, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/scheduler.py", line 120, in enqueue_events_for_site
    enqueue_events()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/scheduler.py", line 140, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 74, in enqueue
    if not self.is_job_in_queue():
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 96, in is_job_in_queue
    return is_job_enqueued(self.rq_job_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 665, in is_job_enqueued
    return get_job_status(job_id) in (JobStatus.QUEUED, JobStatus.STARTED)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 670, in get_job_status
    if job := get_job(job_id):
              ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 676, in get_job
    return Job.fetch(create_job_id(job_id), connection=get_redis_conn())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/rq/job.py", line 674, in fetch
    job.refresh()
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/rq/job.py", line 1037, in refresh
    data = self.connection.hgetall(self.key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/commands/core.py", line 4959, in hgetall
    return self.execute_command("HGETALL", name, keys=[name])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/client.py", line 623, in execute_command
    return self._execute_command(*args, **options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/client.py", line 629, in _execute_command
    conn = self.connection or pool.get_connection()
                              ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/utils.py", line 191, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 1540, in get_connection
    connection.connect()
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 379, in connect
    self.connect_check_health(check_health=True)
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 391, in connect_check_health
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11004. Connection refused.
2025-07-01 15:32:00,710 ERROR scheduler Exception in Enqueue Events for Site work
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 385, in connect_check_health
    sock = self.retry.call_with_retry(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/retry.py", line 87, in call_with_retry
    return do()
           ^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 386, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
            ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 797, in _connect
    raise err
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 781, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/scheduler.py", line 120, in enqueue_events_for_site
    enqueue_events()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/scheduler.py", line 140, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 74, in enqueue
    if not self.is_job_in_queue():
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 96, in is_job_in_queue
    return is_job_enqueued(self.rq_job_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 665, in is_job_enqueued
    return get_job_status(job_id) in (JobStatus.QUEUED, JobStatus.STARTED)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 670, in get_job_status
    if job := get_job(job_id):
              ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 676, in get_job
    return Job.fetch(create_job_id(job_id), connection=get_redis_conn())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/rq/job.py", line 674, in fetch
    job.refresh()
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/rq/job.py", line 1037, in refresh
    data = self.connection.hgetall(self.key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/commands/core.py", line 4959, in hgetall
    return self.execute_command("HGETALL", name, keys=[name])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/client.py", line 623, in execute_command
    return self._execute_command(*args, **options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/client.py", line 629, in _execute_command
    conn = self.connection or pool.get_connection()
                              ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/utils.py", line 191, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 1530, in get_connection
    connection.connect()
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 379, in connect
    self.connect_check_health(check_health=True)
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 391, in connect_check_health
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11004. Connection refused.
2025-07-01 15:36:00,821 ERROR scheduler Exception in Enqueue Events for Site work
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 385, in connect_check_health
    sock = self.retry.call_with_retry(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/retry.py", line 87, in call_with_retry
    return do()
           ^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 386, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
            ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 797, in _connect
    raise err
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 781, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/scheduler.py", line 120, in enqueue_events_for_site
    enqueue_events()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/scheduler.py", line 140, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 74, in enqueue
    if not self.is_job_in_queue():
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 96, in is_job_in_queue
    return is_job_enqueued(self.rq_job_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 665, in is_job_enqueued
    return get_job_status(job_id) in (JobStatus.QUEUED, JobStatus.STARTED)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 670, in get_job_status
    if job := get_job(job_id):
              ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 676, in get_job
    return Job.fetch(create_job_id(job_id), connection=get_redis_conn())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/rq/job.py", line 674, in fetch
    job.refresh()
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/rq/job.py", line 1037, in refresh
    data = self.connection.hgetall(self.key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/commands/core.py", line 4959, in hgetall
    return self.execute_command("HGETALL", name, keys=[name])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/client.py", line 623, in execute_command
    return self._execute_command(*args, **options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/client.py", line 629, in _execute_command
    conn = self.connection or pool.get_connection()
                              ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/utils.py", line 191, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 1530, in get_connection
    connection.connect()
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 379, in connect
    self.connect_check_health(check_health=True)
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 391, in connect_check_health
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11004. Connection refused.
2025-07-01 15:40:00,869 ERROR scheduler Exception in Enqueue Events for Site work
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 385, in connect_check_health
    sock = self.retry.call_with_retry(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/retry.py", line 87, in call_with_retry
    return do()
           ^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 386, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
            ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 797, in _connect
    raise err
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 781, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/scheduler.py", line 120, in enqueue_events_for_site
    enqueue_events()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/scheduler.py", line 140, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 74, in enqueue
    if not self.is_job_in_queue():
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 96, in is_job_in_queue
    return is_job_enqueued(self.rq_job_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 665, in is_job_enqueued
    return get_job_status(job_id) in (JobStatus.QUEUED, JobStatus.STARTED)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 670, in get_job_status
    if job := get_job(job_id):
              ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 676, in get_job
    return Job.fetch(create_job_id(job_id), connection=get_redis_conn())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/rq/job.py", line 674, in fetch
    job.refresh()
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/rq/job.py", line 1037, in refresh
    data = self.connection.hgetall(self.key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/commands/core.py", line 4959, in hgetall
    return self.execute_command("HGETALL", name, keys=[name])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/client.py", line 623, in execute_command
    return self._execute_command(*args, **options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/client.py", line 629, in _execute_command
    conn = self.connection or pool.get_connection()
                              ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/utils.py", line 191, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 1530, in get_connection
    connection.connect()
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 379, in connect
    self.connect_check_health(check_health=True)
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 391, in connect_check_health
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11004. Connection refused.
2025-07-01 15:44:00,964 ERROR scheduler Exception in Enqueue Events for Site work
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 385, in connect_check_health
    sock = self.retry.call_with_retry(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/retry.py", line 87, in call_with_retry
    return do()
           ^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 386, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
            ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 797, in _connect
    raise err
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 781, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/scheduler.py", line 120, in enqueue_events_for_site
    enqueue_events()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/scheduler.py", line 140, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 74, in enqueue
    if not self.is_job_in_queue():
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 96, in is_job_in_queue
    return is_job_enqueued(self.rq_job_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 665, in is_job_enqueued
    return get_job_status(job_id) in (JobStatus.QUEUED, JobStatus.STARTED)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 670, in get_job_status
    if job := get_job(job_id):
              ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 676, in get_job
    return Job.fetch(create_job_id(job_id), connection=get_redis_conn())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/rq/job.py", line 674, in fetch
    job.refresh()
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/rq/job.py", line 1037, in refresh
    data = self.connection.hgetall(self.key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/commands/core.py", line 4959, in hgetall
    return self.execute_command("HGETALL", name, keys=[name])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/client.py", line 623, in execute_command
    return self._execute_command(*args, **options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/client.py", line 629, in _execute_command
    conn = self.connection or pool.get_connection()
                              ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/utils.py", line 191, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 1530, in get_connection
    connection.connect()
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 379, in connect
    self.connect_check_health(check_health=True)
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 391, in connect_check_health
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11004. Connection refused.
2025-07-01 15:48:01,026 ERROR scheduler Exception in Enqueue Events for Site work
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 385, in connect_check_health
    sock = self.retry.call_with_retry(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/retry.py", line 87, in call_with_retry
    return do()
           ^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 386, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
            ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 797, in _connect
    raise err
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 781, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/scheduler.py", line 120, in enqueue_events_for_site
    enqueue_events()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/scheduler.py", line 140, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 74, in enqueue
    if not self.is_job_in_queue():
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 96, in is_job_in_queue
    return is_job_enqueued(self.rq_job_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 665, in is_job_enqueued
    return get_job_status(job_id) in (JobStatus.QUEUED, JobStatus.STARTED)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 670, in get_job_status
    if job := get_job(job_id):
              ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 676, in get_job
    return Job.fetch(create_job_id(job_id), connection=get_redis_conn())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/rq/job.py", line 674, in fetch
    job.refresh()
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/rq/job.py", line 1037, in refresh
    data = self.connection.hgetall(self.key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/commands/core.py", line 4959, in hgetall
    return self.execute_command("HGETALL", name, keys=[name])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/client.py", line 623, in execute_command
    return self._execute_command(*args, **options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/client.py", line 629, in _execute_command
    conn = self.connection or pool.get_connection()
                              ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/utils.py", line 191, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 1530, in get_connection
    connection.connect()
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 379, in connect
    self.connect_check_health(check_health=True)
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 391, in connect_check_health
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11004. Connection refused.
2025-07-01 15:52:00,105 ERROR scheduler Exception in Enqueue Events for Site work
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 385, in connect_check_health
    sock = self.retry.call_with_retry(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/retry.py", line 87, in call_with_retry
    return do()
           ^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 386, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
            ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 797, in _connect
    raise err
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 781, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/scheduler.py", line 120, in enqueue_events_for_site
    enqueue_events()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/scheduler.py", line 140, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 74, in enqueue
    if not self.is_job_in_queue():
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 96, in is_job_in_queue
    return is_job_enqueued(self.rq_job_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 665, in is_job_enqueued
    return get_job_status(job_id) in (JobStatus.QUEUED, JobStatus.STARTED)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 670, in get_job_status
    if job := get_job(job_id):
              ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 676, in get_job
    return Job.fetch(create_job_id(job_id), connection=get_redis_conn())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/rq/job.py", line 674, in fetch
    job.refresh()
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/rq/job.py", line 1037, in refresh
    data = self.connection.hgetall(self.key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/commands/core.py", line 4959, in hgetall
    return self.execute_command("HGETALL", name, keys=[name])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/client.py", line 623, in execute_command
    return self._execute_command(*args, **options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/client.py", line 629, in _execute_command
    conn = self.connection or pool.get_connection()
                              ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/utils.py", line 191, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 1530, in get_connection
    connection.connect()
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 379, in connect
    self.connect_check_health(check_health=True)
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 391, in connect_check_health
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11004. Connection refused.
2025-07-01 15:56:00,195 ERROR scheduler Exception in Enqueue Events for Site work
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 385, in connect_check_health
    sock = self.retry.call_with_retry(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/retry.py", line 87, in call_with_retry
    return do()
           ^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 386, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
            ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 797, in _connect
    raise err
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 781, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/scheduler.py", line 120, in enqueue_events_for_site
    enqueue_events()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/scheduler.py", line 140, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 74, in enqueue
    if not self.is_job_in_queue():
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 96, in is_job_in_queue
    return is_job_enqueued(self.rq_job_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 665, in is_job_enqueued
    return get_job_status(job_id) in (JobStatus.QUEUED, JobStatus.STARTED)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 670, in get_job_status
    if job := get_job(job_id):
              ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 676, in get_job
    return Job.fetch(create_job_id(job_id), connection=get_redis_conn())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/rq/job.py", line 674, in fetch
    job.refresh()
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/rq/job.py", line 1037, in refresh
    data = self.connection.hgetall(self.key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/commands/core.py", line 4959, in hgetall
    return self.execute_command("HGETALL", name, keys=[name])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/client.py", line 623, in execute_command
    return self._execute_command(*args, **options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/client.py", line 629, in _execute_command
    conn = self.connection or pool.get_connection()
                              ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/utils.py", line 191, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 1530, in get_connection
    connection.connect()
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 379, in connect
    self.connect_check_health(check_health=True)
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 391, in connect_check_health
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11004. Connection refused.
2025-07-01 16:00:00,287 ERROR scheduler Exception in Enqueue Events for Site work
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 385, in connect_check_health
    sock = self.retry.call_with_retry(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/retry.py", line 87, in call_with_retry
    return do()
           ^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 386, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
            ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 797, in _connect
    raise err
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 781, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/scheduler.py", line 120, in enqueue_events_for_site
    enqueue_events()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/scheduler.py", line 140, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 74, in enqueue
    if not self.is_job_in_queue():
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 96, in is_job_in_queue
    return is_job_enqueued(self.rq_job_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 665, in is_job_enqueued
    return get_job_status(job_id) in (JobStatus.QUEUED, JobStatus.STARTED)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 670, in get_job_status
    if job := get_job(job_id):
              ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 676, in get_job
    return Job.fetch(create_job_id(job_id), connection=get_redis_conn())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/rq/job.py", line 674, in fetch
    job.refresh()
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/rq/job.py", line 1037, in refresh
    data = self.connection.hgetall(self.key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/commands/core.py", line 4959, in hgetall
    return self.execute_command("HGETALL", name, keys=[name])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/client.py", line 623, in execute_command
    return self._execute_command(*args, **options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/client.py", line 629, in _execute_command
    conn = self.connection or pool.get_connection()
                              ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/utils.py", line 191, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 1530, in get_connection
    connection.connect()
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 379, in connect
    self.connect_check_health(check_health=True)
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 391, in connect_check_health
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11004. Connection refused.
2025-07-01 16:04:00,424 ERROR scheduler Exception in Enqueue Events for Site work
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 385, in connect_check_health
    sock = self.retry.call_with_retry(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/retry.py", line 87, in call_with_retry
    return do()
           ^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 386, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
            ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 797, in _connect
    raise err
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 781, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/scheduler.py", line 120, in enqueue_events_for_site
    enqueue_events()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/scheduler.py", line 140, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 74, in enqueue
    if not self.is_job_in_queue():
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 96, in is_job_in_queue
    return is_job_enqueued(self.rq_job_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 665, in is_job_enqueued
    return get_job_status(job_id) in (JobStatus.QUEUED, JobStatus.STARTED)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 670, in get_job_status
    if job := get_job(job_id):
              ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 676, in get_job
    return Job.fetch(create_job_id(job_id), connection=get_redis_conn())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/rq/job.py", line 674, in fetch
    job.refresh()
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/rq/job.py", line 1037, in refresh
    data = self.connection.hgetall(self.key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/commands/core.py", line 4959, in hgetall
    return self.execute_command("HGETALL", name, keys=[name])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/client.py", line 623, in execute_command
    return self._execute_command(*args, **options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/client.py", line 629, in _execute_command
    conn = self.connection or pool.get_connection()
                              ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/utils.py", line 191, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 1530, in get_connection
    connection.connect()
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 379, in connect
    self.connect_check_health(check_health=True)
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 391, in connect_check_health
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11004. Connection refused.
2025-07-01 16:08:00,564 ERROR scheduler Exception in Enqueue Events for Site work
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 385, in connect_check_health
    sock = self.retry.call_with_retry(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/retry.py", line 87, in call_with_retry
    return do()
           ^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 386, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
            ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 797, in _connect
    raise err
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 781, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/scheduler.py", line 120, in enqueue_events_for_site
    enqueue_events()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/scheduler.py", line 140, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 74, in enqueue
    if not self.is_job_in_queue():
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 96, in is_job_in_queue
    return is_job_enqueued(self.rq_job_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 665, in is_job_enqueued
    return get_job_status(job_id) in (JobStatus.QUEUED, JobStatus.STARTED)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 670, in get_job_status
    if job := get_job(job_id):
              ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/background_jobs.py", line 676, in get_job
    return Job.fetch(create_job_id(job_id), connection=get_redis_conn())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/rq/job.py", line 674, in fetch
    job.refresh()
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/rq/job.py", line 1037, in refresh
    data = self.connection.hgetall(self.key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/commands/core.py", line 4959, in hgetall
    return self.execute_command("HGETALL", name, keys=[name])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/client.py", line 623, in execute_command
    return self._execute_command(*args, **options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/client.py", line 629, in _execute_command
    conn = self.connection or pool.get_connection()
                              ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/utils.py", line 191, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 1530, in get_connection
    connection.connect()
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 379, in connect
    self.connect_check_health(check_health=True)
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/redis/connection.py", line 391, in connect_check_health
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to 127.0.0.1:11004. Connection refused.
