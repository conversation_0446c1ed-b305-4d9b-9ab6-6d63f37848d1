_HiStOrY_V2_
SELECT\040value\040FROM\040`tabSingles`\040WHERE\040doctype\040=\040'DefaultValue'\040AND\040defkey\040=\040'installed_apps';
DESCRIBE\040`tabSingles`;
SELECT\040value\040FROM\040`tabSingles`\040WHERE\040doctype\040=\040'DefaultValue'\040AND\040field\040=\040'installed_apps';
SELECT\040*\040FROM\040`tabSingles`\040WHERE\040value\040LIKE\040'%webshop%';
SHOW\040TABLES\040LIKE\040'%webshop%';
SELECT\040*\040FROM\040`tabDocType`\040WHERE\040name\040LIKE\040'%webshop%'\040OR\040module\040LIKE\040'%webshop%';
SELECT\040DISTINCT\040doctype\040FROM\040`tabSingles`\040WHERE\040doctype\040LIKE\040'%webshop%'\040OR\040doctype\040LIKE\040'%Webshop%';
SELECT\040*\040FROM\040`tabSingles`\040WHERE\040doctype\040=\040'Webshop\040Settings';
DELETE\040FROM\040`tabSingles`\040WHERE\040doctype\040=\040'Webshop\040Settings';
SELECT\040*\040FROM\040`tabDocType`\040WHERE\040name\040=\040'Webshop\040Settings';
SELECT\040table_name\040FROM\040information_schema.tables\040WHERE\040table_schema\040=\040DATABASE()\040AND\040table_name\040LIKE\040'%webshop%';
SELECT\040*\040FROM\040`tabSingles`\040WHERE\040value\040LIKE\040'%webshop%'\040OR\040value\040LIKE\040'%Webshop%';
SELECT\040*\040FROM\040`tabModule\040Def`\040WHERE\040name\040LIKE\040'%webshop%'\040OR\040name\040LIKE\040'%Webshop%';
SELECT\040*\040FROM\040`tabInstalled\040Applications`\040WHERE\040app_name\040LIKE\040'%webshop%';
SHOW\040TABLES\040LIKE\040'%installed%';
exit;
SELECT\040*\040FROM\040`tabSingles`\040WHERE\040value\040LIKE\040'%webshop%'\040OR\040value\040LIKE\040'%Webshop%';
SELECT\040*\040FROM\040`tabSingles`\040WHERE\040doctype\040=\040'DefaultValue'\040AND\040field\040=\040'installed_apps';
SELECT\040*\040FROM\040`tabSingles`\040WHERE\040doctype\040=\040'System\040Settings';
SELECT\040*\040FROM\040`tabDefaultValue`\040WHERE\040defkey\040=\040'installed_apps';
SELECT\040defvalue\040FROM\040`tabDefaultValue`\040WHERE\040defkey\040=\040'installed_apps';
UPDATE\040`tabDefaultValue`\040SET\040defvalue\040=\040'["frappe",\040"erpnext",\040"payments",\040"hrms",\040"ecommerce",\040"crm"]'\040WHERE\040defkey\040=\040'installed_apps';
SET\040SQL_SAFE_UPDATES\040=\0400;
UPDATE\040`tabDefaultValue`\040SET\040defvalue\040=\040'["frappe",\040"erpnext",\040"payments",\040"hrms",\040"ecommerce",\040"crm"]'\040WHERE\040defkey\040=\040'installed_apps';
SELECT\040defvalue\040FROM\040`tabDefaultValue`\040WHERE\040defkey\040=\040'installed_apps';
exit;
