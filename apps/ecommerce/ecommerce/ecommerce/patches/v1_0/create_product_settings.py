import frappe


def execute():
    """Create default Product Settings"""
    
    # Check if Product Settings doctype exists
    if not frappe.db.exists("DocType", "Product Settings"):
        return
    
    # Check if Product Settings document already exists
    if frappe.db.exists("Product Settings", "Product Settings"):
        return
    
    # Create default Product Settings
    settings = frappe.get_doc({
        "doctype": "Product Settings",
        "default_currency": "TZS",
        "enable_inventory_tracking": 1,
        "auto_generate_sku": 1,
        "sku_prefix": "SKU",
        "default_shipping_class": "Standard",
        "enable_compare_pricing": 1,
        "max_discount_percentage": 50,
        "default_min_stock_level": 10,
        "default_max_stock_level": 1000,
        "enable_low_stock_alerts": 1,
        "low_stock_threshold": 5,
        "default_product_status": "Active",
        "enable_product_reviews": 1,
        "enable_product_ratings": 1,
        "enable_related_products": 1,
        "max_related_products": 6,
        "max_product_images": 10,
        "default_image_quality": "Medium",
        "enable_image_compression": 1,
        "image_resize_dimensions": "800x600",
        "auto_generate_meta_title": 1,
        "auto_generate_meta_description": 1,
        "enable_structured_data": 1,
        "enable_sitemap": 1,
        "enable_breadcrumbs": 1,
        "canonical_url_format": "Product Name",
        "default_shipping_required": 1,
        "default_shipping_weight": 1.0,
        "default_shipping_dimensions": "10x10x10",
        "enable_shipping_calculator": 1,
        "enable_expedited_shipping": 1,
        "enable_stock_notifications": 1,
        "enable_new_product_notifications": 1,
        "enable_customer_notifications": 1,
        "enable_admin_notifications": 1,
        "notification_frequency": "Immediate",
        "sync_with_erpnext_items": 1,
        "auto_create_item_groups": 1,
        "enable_accounting_integration": 1,
        "enable_analytics_tracking": 1,
        "analytics_provider": "Google Analytics"
    })
    
    settings.insert(ignore_permissions=True)
    frappe.db.commit()
    
    print("✅ Default Product Settings created successfully")
