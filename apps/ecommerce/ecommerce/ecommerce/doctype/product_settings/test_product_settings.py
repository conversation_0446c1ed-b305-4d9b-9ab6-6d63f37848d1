# Copyright (c) 2025, <PERSON> and Contributors
# See license.txt

import frappe
import unittest
from frappe.utils import flt, cint


class TestProductSettings(unittest.TestCase):
    def setUp(self):
        """Set up test data"""
        # Clean up any existing settings
        if frappe.db.exists("Product Settings", "Product Settings"):
            frappe.delete_doc("Product Settings", "Product Settings")

    def test_create_default_settings(self):
        """Test creating default product settings"""
        from ecommerce.ecommerce.doctype.product_settings.product_settings import get_product_settings
        
        settings = get_product_settings()
        self.assertTrue(settings)
        self.assertEqual(settings.default_currency, "TZS")
        self.assertEqual(settings.enable_inventory_tracking, 1)

    def test_validate_pricing_settings(self):
        """Test pricing settings validation"""
        settings = frappe.get_doc({
            "doctype": "Product Settings",
            "max_discount_percentage": 150  # Invalid - over 100%
        })
        
        with self.assertRaises(frappe.ValidationE<PERSON>):
            settings.validate()

    def test_validate_inventory_settings(self):
        """Test inventory settings validation"""
        settings = frappe.get_doc({
            "doctype": "Product Settings",
            "default_min_stock_level": 100,
            "default_max_stock_level": 50  # Invalid - min > max
        })
        
        with self.assertRaises(frappe.ValidationError):
            settings.validate()

    def test_validate_image_settings(self):
        """Test image settings validation"""
        settings = frappe.get_doc({
            "doctype": "Product Settings",
            "image_resize_dimensions": "invalid_format"  # Invalid format
        })
        
        with self.assertRaises(frappe.ValidationError):
            settings.validate()

    def test_validate_shipping_settings(self):
        """Test shipping settings validation"""
        settings = frappe.get_doc({
            "doctype": "Product Settings",
            "default_shipping_dimensions": "10x10"  # Invalid - missing height
        })
        
        with self.assertRaises(frappe.ValidationError):
            settings.validate()

    def test_get_default_product_values(self):
        """Test getting default values for products"""
        from ecommerce.ecommerce.doctype.product_settings.product_settings import get_default_product_values
        
        defaults = get_default_product_values()
        self.assertTrue(isinstance(defaults, dict))
        self.assertIn("currency", defaults)
        self.assertIn("track_inventory", defaults)

    def test_generate_sku(self):
        """Test SKU generation"""
        from ecommerce.ecommerce.doctype.product_settings.product_settings import generate_sku
        
        # Create settings with auto SKU generation
        settings = frappe.get_doc({
            "doctype": "Product Settings",
            "auto_generate_sku": 1,
            "sku_prefix": "TEST"
        })
        settings.insert()
        
        sku = generate_sku("Test Product", "TP001")
        self.assertTrue(sku.startswith("TEST-"))

    def test_validate_product_data(self):
        """Test product data validation against settings"""
        from ecommerce.ecommerce.doctype.product_settings.product_settings import validate_product_data
        
        # Create settings
        settings = frappe.get_doc({
            "doctype": "Product Settings",
            "max_discount_percentage": 30,
            "max_product_images": 5
        })
        settings.insert()
        
        # Test valid data
        valid_data = {
            "discount_percentage": 25,
            "product_images": ["img1.jpg", "img2.jpg"]
        }
        result = validate_product_data(valid_data)
        self.assertTrue(result["valid"])
        
        # Test invalid data
        invalid_data = {
            "discount_percentage": 50,  # Exceeds max
            "product_images": ["img1.jpg", "img2.jpg", "img3.jpg", "img4.jpg", "img5.jpg", "img6.jpg"]  # Too many
        }
        result = validate_product_data(invalid_data)
        self.assertFalse(result["valid"])
        self.assertTrue(len(result["errors"]) > 0)

    def tearDown(self):
        """Clean up test data"""
        if frappe.db.exists("Product Settings", "Product Settings"):
            frappe.delete_doc("Product Settings", "Product Settings")
