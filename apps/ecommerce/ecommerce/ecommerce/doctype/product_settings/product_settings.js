// Copyright (c) 2025, <PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on('Product Settings', {
    refresh: function(frm) {
        // Add custom buttons
        frm.add_custom_button(__('Apply to Existing Products'), function() {
            apply_settings_to_products(frm);
        }, __('Actions'));

        frm.add_custom_button(__('Reset to Defaults'), function() {
            reset_to_defaults(frm);
        }, __('Actions'));

        frm.add_custom_button(__('Test Settings'), function() {
            test_settings(frm);
        }, __('Actions'));

        // Set field dependencies
        set_field_dependencies(frm);
    },

    enable_image_watermark: function(frm) {
        frm.toggle_reqd('watermark_image', frm.doc.enable_image_watermark);
    },

    enable_free_shipping_threshold: function(frm) {
        frm.toggle_reqd('free_shipping_amount', frm.doc.enable_free_shipping_threshold);
    },

    enable_low_stock_alerts: function(frm) {
        frm.toggle_reqd('low_stock_threshold', frm.doc.enable_low_stock_alerts);
    },

    auto_generate_sku: function(frm) {
        frm.toggle_reqd('sku_prefix', frm.doc.auto_generate_sku);
    },

    enable_analytics_tracking: function(frm) {
        frm.toggle_reqd('analytics_provider', frm.doc.enable_analytics_tracking);
    },

    max_discount_percentage: function(frm) {
        if (frm.doc.max_discount_percentage > 100) {
            frappe.msgprint(__('Maximum discount percentage cannot exceed 100%'));
            frm.set_value('max_discount_percentage', 100);
        }
    },

    image_resize_dimensions: function(frm) {
        validate_dimensions_format(frm, 'image_resize_dimensions', 'WIDTHxHEIGHT');
    },

    default_shipping_dimensions: function(frm) {
        validate_dimensions_format(frm, 'default_shipping_dimensions', 'LxWxH');
    }
});

function set_field_dependencies(frm) {
    // Set up field dependencies and visibility
    frm.toggle_display('watermark_image', frm.doc.enable_image_watermark);
    frm.toggle_display('watermark_position', frm.doc.enable_image_watermark);
    frm.toggle_display('free_shipping_amount', frm.doc.enable_free_shipping_threshold);
    frm.toggle_display('low_stock_threshold', frm.doc.enable_low_stock_alerts);
    frm.toggle_display('sku_prefix', frm.doc.auto_generate_sku);
    frm.toggle_display('analytics_provider', frm.doc.enable_analytics_tracking);
}

function validate_dimensions_format(frm, fieldname, format) {
    const value = frm.doc[fieldname];
    if (!value) return;

    const parts = value.split('x');
    const expected_parts = format === 'WIDTHxHEIGHT' ? 2 : 3;
    
    if (parts.length !== expected_parts) {
        frappe.msgprint(__(`Invalid format. Please use ${format} format (e.g., ${format === 'WIDTHxHEIGHT' ? '800x600' : '10x10x10'})`));
        frm.set_value(fieldname, '');
        return;
    }

    // Validate that all parts are positive numbers
    for (let part of parts) {
        if (isNaN(part) || parseFloat(part) <= 0) {
            frappe.msgprint(__('All dimensions must be positive numbers'));
            frm.set_value(fieldname, '');
            return;
        }
    }
}

function apply_settings_to_products(frm) {
    frappe.confirm(
        __('This will apply current settings to all existing products. This action cannot be undone. Continue?'),
        function() {
            frappe.call({
                method: 'ecommerce.ecommerce.doctype.product_settings.product_settings.apply_settings_to_products',
                callback: function(r) {
                    if (r.message && r.message.status === 'success') {
                        frappe.msgprint(__(r.message.message));
                    }
                }
            });
        }
    );
}

function reset_to_defaults(frm) {
    frappe.confirm(
        __('This will reset all settings to default values. Continue?'),
        function() {
            // Reset to default values
            const defaults = {
                default_currency: 'TZS',
                enable_inventory_tracking: 1,
                auto_generate_sku: 1,
                sku_prefix: 'SKU',
                default_shipping_class: 'Standard',
                enable_compare_pricing: 1,
                max_discount_percentage: 50,
                default_min_stock_level: 10,
                default_max_stock_level: 1000,
                enable_low_stock_alerts: 1,
                low_stock_threshold: 5,
                default_product_status: 'Active',
                enable_product_reviews: 1,
                enable_product_ratings: 1,
                enable_related_products: 1,
                max_related_products: 6,
                max_product_images: 10,
                default_image_quality: 'Medium',
                enable_image_compression: 1,
                image_resize_dimensions: '800x600',
                auto_generate_meta_title: 1,
                auto_generate_meta_description: 1,
                enable_structured_data: 1,
                enable_sitemap: 1,
                enable_breadcrumbs: 1,
                canonical_url_format: 'Product Name',
                default_shipping_required: 1,
                default_shipping_weight: 1.0,
                default_shipping_dimensions: '10x10x10',
                enable_shipping_calculator: 1,
                enable_expedited_shipping: 1,
                enable_stock_notifications: 1,
                enable_new_product_notifications: 1,
                enable_customer_notifications: 1,
                enable_admin_notifications: 1,
                notification_frequency: 'Immediate',
                sync_with_erpnext_items: 1,
                auto_create_item_groups: 1,
                enable_accounting_integration: 1,
                enable_analytics_tracking: 1,
                analytics_provider: 'Google Analytics'
            };

            for (let field in defaults) {
                frm.set_value(field, defaults[field]);
            }

            frappe.msgprint(__('Settings reset to defaults'));
        }
    );
}

function test_settings(frm) {
    // Test current settings with sample data
    const test_data = {
        discount_percentage: 25,
        product_images: ['img1.jpg', 'img2.jpg', 'img3.jpg'],
        min_stock_level: 5,
        max_stock_level: 100
    };

    frappe.call({
        method: 'ecommerce.ecommerce.doctype.product_settings.product_settings.validate_product_data',
        args: {
            product_data: test_data
        },
        callback: function(r) {
            if (r.message) {
                if (r.message.valid) {
                    frappe.msgprint(__('✅ Settings validation passed! Sample product data is valid.'));
                } else {
                    let error_msg = __('❌ Settings validation failed:') + '<br>';
                    r.message.errors.forEach(function(error) {
                        error_msg += '• ' + error + '<br>';
                    });
                    frappe.msgprint(error_msg);
                }
            }
        }
    });
}
