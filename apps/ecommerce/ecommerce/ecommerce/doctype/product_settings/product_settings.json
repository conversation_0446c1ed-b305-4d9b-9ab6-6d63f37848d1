{"actions": [], "creation": "2025-07-01 16:20:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["general_settings_section", "default_currency", "default_tax_category", "default_shipping_class", "enable_inventory_tracking", "column_break_5", "auto_generate_sku", "sku_prefix", "enable_barcode_generation", "default_weight_unit", "pricing_settings_section", "enable_compare_pricing", "enable_bulk_pricing", "enable_dynamic_pricing", "default_discount_type", "column_break_14", "max_discount_percentage", "enable_tax_inclusive_pricing", "round_pricing_to", "inventory_settings_section", "default_min_stock_level", "default_max_stock_level", "enable_low_stock_alerts", "low_stock_threshold", "column_break_23", "enable_backorders", "enable_stock_reservations", "auto_update_stock", "product_display_settings_section", "default_product_status", "auto_publish_products", "enable_product_reviews", "enable_product_ratings", "column_break_32", "default_featured_status", "enable_related_products", "max_related_products", "image_settings_section", "max_product_images", "default_image_quality", "enable_image_compression", "image_resize_dimensions", "column_break_41", "enable_image_watermark", "watermark_image", "watermark_position", "seo_settings_section", "auto_generate_meta_title", "auto_generate_meta_description", "enable_structured_data", "default_meta_keywords", "column_break_50", "enable_sitemap", "enable_breadcrumbs", "canonical_url_format", "shipping_settings_section", "default_shipping_required", "enable_free_shipping_threshold", "free_shipping_amount", "default_shipping_weight", "column_break_59", "enable_shipping_calculator", "default_shipping_dimensions", "enable_expedited_shipping", "notification_settings_section", "enable_stock_notifications", "enable_price_change_notifications", "enable_new_product_notifications", "notification_email", "column_break_68", "enable_customer_notifications", "enable_admin_notifications", "notification_frequency", "integration_settings_section", "sync_with_erpnext_items", "auto_create_item_groups", "enable_accounting_integration", "default_income_account", "column_break_77", "default_expense_account", "enable_analytics_tracking", "analytics_provider"], "fields": [{"fieldname": "general_settings_section", "fieldtype": "Section Break", "label": "General Settings"}, {"fieldname": "default_currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>", "default": "TZS", "reqd": 1}, {"fieldname": "default_tax_category", "fieldtype": "Link", "label": "Default Tax Category", "options": "Tax Category"}, {"fieldname": "default_shipping_class", "fieldtype": "Select", "label": "Default Shipping Class", "options": "Standard\nExpress\nFree\nHeavy\nFragile", "default": "Standard"}, {"fieldname": "enable_inventory_tracking", "fieldtype": "Check", "label": "Enable Inventory Tracking", "default": "1"}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "auto_generate_sku", "fieldtype": "Check", "label": "Auto Generate SKU", "default": "1"}, {"fieldname": "sku_prefix", "fieldtype": "Data", "label": "SKU Prefix", "default": "SKU", "depends_on": "auto_generate_sku"}, {"fieldname": "enable_barcode_generation", "fieldtype": "Check", "label": "Enable Barcode Generation", "default": "0"}, {"fieldname": "default_weight_unit", "fieldtype": "Select", "label": "Default Weight Unit", "options": "kg\ng\nlb\noz", "default": "kg"}, {"fieldname": "pricing_settings_section", "fieldtype": "Section Break", "label": "Pricing Settings"}, {"fieldname": "enable_compare_pricing", "fieldtype": "Check", "label": "Enable Compare Pricing", "default": "1"}, {"fieldname": "enable_bulk_pricing", "fieldtype": "Check", "label": "Enable Bulk Pricing", "default": "0"}, {"fieldname": "enable_dynamic_pricing", "fieldtype": "Check", "label": "Enable Dynamic Pricing", "default": "0"}, {"fieldname": "default_discount_type", "fieldtype": "Select", "label": "Default Discount Type", "options": "Percentage\nFixed Amount", "default": "Percentage"}, {"fieldname": "column_break_14", "fieldtype": "Column Break"}, {"fieldname": "max_discount_percentage", "fieldtype": "Percent", "label": "Maximum Discount Percentage", "default": "50"}, {"fieldname": "enable_tax_inclusive_pricing", "fieldtype": "Check", "label": "Enable Tax Inclusive Pricing", "default": "0"}, {"fieldname": "round_pricing_to", "fieldtype": "Select", "label": "Round Pricing To", "options": "0.01\n0.05\n0.10\n0.50\n1.00", "default": "0.01"}, {"fieldname": "inventory_settings_section", "fieldtype": "Section Break", "label": "Inventory Settings"}, {"fieldname": "default_min_stock_level", "fieldtype": "Int", "label": "Default Minimum Stock Level", "default": "10"}, {"fieldname": "default_max_stock_level", "fieldtype": "Int", "label": "Default Maximum Stock Level", "default": "1000"}, {"fieldname": "enable_low_stock_alerts", "fieldtype": "Check", "label": "Enable Low Stock Alerts", "default": "1"}, {"fieldname": "low_stock_threshold", "fieldtype": "Int", "label": "Low Stock Threshold", "default": "5", "depends_on": "enable_low_stock_alerts"}, {"fieldname": "column_break_23", "fieldtype": "Column Break"}, {"fieldname": "enable_backorders", "fieldtype": "Check", "label": "Enable Backorders", "default": "0"}, {"fieldname": "enable_stock_reservations", "fieldtype": "Check", "label": "Enable Stock Reservations", "default": "1"}, {"fieldname": "auto_update_stock", "fieldtype": "Check", "label": "Auto Update Stock on Orders", "default": "1"}, {"fieldname": "product_display_settings_section", "fieldtype": "Section Break", "label": "Product Display Settings"}, {"fieldname": "default_product_status", "fieldtype": "Select", "label": "Default Product Status", "options": "Active\nInactive\nDraft", "default": "Active"}, {"fieldname": "auto_publish_products", "fieldtype": "Check", "label": "Auto Publish Products", "default": "0"}, {"fieldname": "enable_product_reviews", "fieldtype": "Check", "label": "Enable Product Reviews", "default": "1"}, {"fieldname": "enable_product_ratings", "fieldtype": "Check", "label": "Enable Product Ratings", "default": "1"}, {"fieldname": "column_break_32", "fieldtype": "Column Break"}, {"fieldname": "default_featured_status", "fieldtype": "Check", "label": "Default Featured Status", "default": "0"}, {"fieldname": "enable_related_products", "fieldtype": "Check", "label": "Enable Related Products", "default": "1"}, {"fieldname": "max_related_products", "fieldtype": "Int", "label": "Maximum Related Products", "default": "6", "depends_on": "enable_related_products"}, {"fieldname": "image_settings_section", "fieldtype": "Section Break", "label": "Image Settings"}, {"fieldname": "max_product_images", "fieldtype": "Int", "label": "Maximum Product Images", "default": "10"}, {"fieldname": "default_image_quality", "fieldtype": "Select", "label": "Default Image Quality", "options": "High\nMedium\nLow", "default": "Medium"}, {"fieldname": "enable_image_compression", "fieldtype": "Check", "label": "Enable Image Compression", "default": "1"}, {"fieldname": "image_resize_dimensions", "fieldtype": "Data", "label": "Image Resize Dimensions", "default": "800x600", "description": "Format: WIDTHxHEIGHT"}, {"fieldname": "column_break_41", "fieldtype": "Column Break"}, {"fieldname": "enable_image_watermark", "fieldtype": "Check", "label": "Enable Image Watermark", "default": "0"}, {"fieldname": "watermark_image", "fieldtype": "Attach Image", "label": "Watermark Image", "depends_on": "enable_image_watermark"}, {"fieldname": "watermark_position", "fieldtype": "Select", "label": "Watermark Position", "options": "Top Left\nTop Right\nBottom Left\nBottom Right\nCenter", "default": "Bottom Right", "depends_on": "enable_image_watermark"}, {"fieldname": "seo_settings_section", "fieldtype": "Section Break", "label": "SEO Settings"}, {"fieldname": "auto_generate_meta_title", "fieldtype": "Check", "label": "Auto Generate Meta Title", "default": "1"}, {"fieldname": "auto_generate_meta_description", "fieldtype": "Check", "label": "Auto Generate Meta Description", "default": "1"}, {"fieldname": "enable_structured_data", "fieldtype": "Check", "label": "Enable Structured Data", "default": "1"}, {"fieldname": "default_meta_keywords", "fieldtype": "Text", "label": "Default Meta Keywords"}, {"fieldname": "column_break_50", "fieldtype": "Column Break"}, {"fieldname": "enable_sitemap", "fieldtype": "Check", "label": "Enable Sitemap", "default": "1"}, {"fieldname": "enable_breadcrumbs", "fieldtype": "Check", "label": "Enable Breadcrumbs", "default": "1"}, {"fieldname": "canonical_url_format", "fieldtype": "Select", "label": "Canonical URL Format", "options": "Product Name\nProduct Code\nSKU", "default": "Product Name"}, {"fieldname": "shipping_settings_section", "fieldtype": "Section Break", "label": "Shipping Settings"}, {"fieldname": "default_shipping_required", "fieldtype": "Check", "label": "Default Shipping Required", "default": "1"}, {"fieldname": "enable_free_shipping_threshold", "fieldtype": "Check", "label": "Enable Free Shipping Threshold", "default": "0"}, {"fieldname": "free_shipping_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Free Shipping Amount", "depends_on": "enable_free_shipping_threshold"}, {"fieldname": "default_shipping_weight", "fieldtype": "Float", "label": "Default Shipping Weight (kg)", "default": "1.0"}, {"fieldname": "column_break_59", "fieldtype": "Column Break"}, {"fieldname": "enable_shipping_calculator", "fieldtype": "Check", "label": "Enable Shipping Calculator", "default": "1"}, {"fieldname": "default_shipping_dimensions", "fieldtype": "Data", "label": "Default Shipping Dimensions", "default": "10x10x10", "description": "Format: LxWxH in cm"}, {"fieldname": "enable_expedited_shipping", "fieldtype": "Check", "label": "Enable Expedited Shipping", "default": "1"}, {"fieldname": "notification_settings_section", "fieldtype": "Section Break", "label": "Notification Settings"}, {"fieldname": "enable_stock_notifications", "fieldtype": "Check", "label": "Enable Stock Notifications", "default": "1"}, {"fieldname": "enable_price_change_notifications", "fieldtype": "Check", "label": "Enable Price Change Notifications", "default": "0"}, {"fieldname": "enable_new_product_notifications", "fieldtype": "Check", "label": "Enable New Product Notifications", "default": "1"}, {"fieldname": "notification_email", "fieldtype": "Data", "label": "Notification Email", "options": "Email"}, {"fieldname": "column_break_68", "fieldtype": "Column Break"}, {"fieldname": "enable_customer_notifications", "fieldtype": "Check", "label": "Enable Customer Notifications", "default": "1"}, {"fieldname": "enable_admin_notifications", "fieldtype": "Check", "label": "Enable Admin Notifications", "default": "1"}, {"fieldname": "notification_frequency", "fieldtype": "Select", "label": "Notification Frequency", "options": "Immediate\nDaily\nWeekly", "default": "Immediate"}, {"fieldname": "integration_settings_section", "fieldtype": "Section Break", "label": "Integration Settings"}, {"fieldname": "sync_with_erpnext_items", "fieldtype": "Check", "label": "Sync with ERPNext Items", "default": "1"}, {"fieldname": "auto_create_item_groups", "fieldtype": "Check", "label": "Auto Create Item Groups", "default": "1"}, {"fieldname": "enable_accounting_integration", "fieldtype": "Check", "label": "Enable Accounting Integration", "default": "1"}, {"fieldname": "default_income_account", "fieldtype": "Link", "label": "Default Income Account", "options": "Account"}, {"fieldname": "column_break_77", "fieldtype": "Column Break"}, {"fieldname": "default_expense_account", "fieldtype": "Link", "label": "De<PERSON>ult Expense Account", "options": "Account"}, {"fieldname": "enable_analytics_tracking", "fieldtype": "Check", "label": "Enable Analytics Tracking", "default": "1"}, {"fieldname": "analytics_provider", "fieldtype": "Select", "label": "Analytics Provider", "options": "Google Analytics\nFacebook Pixel\nCustom", "default": "Google Analytics", "depends_on": "enable_analytics_tracking"}], "index_web_pages_for_search": 1, "issingle": 1, "links": [], "modified": "2025-07-01 16:20:00.000000", "modified_by": "Administrator", "module": "Ecommerce", "name": "Product Settings", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Ecommerce Manager", "share": 1, "write": 1}, {"read": 1, "role": "Ecommerce User", "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}