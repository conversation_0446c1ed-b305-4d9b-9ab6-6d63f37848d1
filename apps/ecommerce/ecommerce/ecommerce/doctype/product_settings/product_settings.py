# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import flt, cint


class ProductSettings(Document):
    def validate(self):
        """Validate Product Settings"""
        self.validate_pricing_settings()
        self.validate_inventory_settings()
        self.validate_image_settings()
        self.validate_shipping_settings()
        self.validate_notification_settings()

    def validate_pricing_settings(self):
        """Validate pricing related settings"""
        if self.max_discount_percentage and flt(self.max_discount_percentage) > 100:
            frappe.throw("Maximum discount percentage cannot exceed 100%")
        
        if self.max_discount_percentage and flt(self.max_discount_percentage) < 0:
            frappe.throw("Maximum discount percentage cannot be negative")

    def validate_inventory_settings(self):
        """Validate inventory related settings"""
        if self.default_min_stock_level and self.default_max_stock_level:
            if cint(self.default_min_stock_level) >= cint(self.default_max_stock_level):
                frappe.throw("Minimum stock level must be less than maximum stock level")
        
        if self.low_stock_threshold and self.default_min_stock_level:
            if cint(self.low_stock_threshold) > cint(self.default_min_stock_level):
                frappe.throw("Low stock threshold cannot be greater than minimum stock level")

    def validate_image_settings(self):
        """Validate image related settings"""
        if self.max_product_images and cint(self.max_product_images) <= 0:
            frappe.throw("Maximum product images must be greater than 0")
        
        if self.image_resize_dimensions:
            try:
                dimensions = self.image_resize_dimensions.split('x')
                if len(dimensions) != 2:
                    raise ValueError
                width, height = int(dimensions[0]), int(dimensions[1])
                if width <= 0 or height <= 0:
                    raise ValueError
            except ValueError:
                frappe.throw("Image resize dimensions must be in format WIDTHxHEIGHT (e.g., 800x600)")

    def validate_shipping_settings(self):
        """Validate shipping related settings"""
        if self.free_shipping_amount and flt(self.free_shipping_amount) <= 0:
            frappe.throw("Free shipping amount must be greater than 0")
        
        if self.default_shipping_weight and flt(self.default_shipping_weight) <= 0:
            frappe.throw("Default shipping weight must be greater than 0")
        
        if self.default_shipping_dimensions:
            try:
                dimensions = self.default_shipping_dimensions.split('x')
                if len(dimensions) != 3:
                    raise ValueError
                length, width, height = float(dimensions[0]), float(dimensions[1]), float(dimensions[2])
                if length <= 0 or width <= 0 or height <= 0:
                    raise ValueError
            except ValueError:
                frappe.throw("Shipping dimensions must be in format LxWxH (e.g., 10x10x10)")

    def validate_notification_settings(self):
        """Validate notification related settings"""
        if self.notification_email and not frappe.utils.validate_email_address(self.notification_email):
            frappe.throw("Please enter a valid notification email address")

    def on_update(self):
        """Actions to perform after updating settings"""
        self.clear_cache()
        self.apply_settings_to_existing_products()

    def clear_cache(self):
        """Clear relevant caches"""
        frappe.cache().delete_key("product_settings")
        frappe.clear_cache(doctype="Product")

    def apply_settings_to_existing_products(self):
        """Apply certain settings to existing products if needed"""
        # This could be used to update existing products with new default values
        # Implementation depends on business requirements
        pass

    def get_default_values_for_product(self):
        """Get default values to be used when creating new products"""
        return {
            "currency": self.default_currency,
            "tax_category": self.default_tax_category,
            "shipping_class": self.default_shipping_class,
            "track_inventory": self.enable_inventory_tracking,
            "min_stock_level": self.default_min_stock_level,
            "max_stock_level": self.default_max_stock_level,
            "is_active": 1 if self.default_product_status == "Active" else 0,
            "is_featured": self.default_featured_status,
            "show_in_website": self.auto_publish_products,
            "requires_shipping": self.default_shipping_required,
            "weight": self.default_shipping_weight,
            "dimensions": self.default_shipping_dimensions
        }


@frappe.whitelist()
def get_product_settings():
    """Get Product Settings document"""
    if not frappe.db.exists("Product Settings", "Product Settings"):
        # Create default settings if not exists
        settings = frappe.get_doc({
            "doctype": "Product Settings",
            "default_currency": "TZS",
            "enable_inventory_tracking": 1,
            "auto_generate_sku": 1,
            "sku_prefix": "SKU",
            "default_shipping_class": "Standard",
            "enable_compare_pricing": 1,
            "max_discount_percentage": 50,
            "default_min_stock_level": 10,
            "default_max_stock_level": 1000,
            "enable_low_stock_alerts": 1,
            "low_stock_threshold": 5,
            "default_product_status": "Active",
            "enable_product_reviews": 1,
            "enable_product_ratings": 1,
            "enable_related_products": 1,
            "max_related_products": 6,
            "max_product_images": 10,
            "default_image_quality": "Medium",
            "enable_image_compression": 1,
            "image_resize_dimensions": "800x600",
            "auto_generate_meta_title": 1,
            "auto_generate_meta_description": 1,
            "enable_structured_data": 1,
            "enable_sitemap": 1,
            "enable_breadcrumbs": 1,
            "canonical_url_format": "Product Name",
            "default_shipping_required": 1,
            "default_shipping_weight": 1.0,
            "default_shipping_dimensions": "10x10x10",
            "enable_shipping_calculator": 1,
            "enable_expedited_shipping": 1,
            "enable_stock_notifications": 1,
            "enable_new_product_notifications": 1,
            "enable_customer_notifications": 1,
            "enable_admin_notifications": 1,
            "notification_frequency": "Immediate",
            "sync_with_erpnext_items": 1,
            "auto_create_item_groups": 1,
            "enable_accounting_integration": 1,
            "enable_analytics_tracking": 1,
            "analytics_provider": "Google Analytics"
        })
        settings.insert()
        return settings
    else:
        return frappe.get_single("Product Settings")


@frappe.whitelist()
def get_default_product_values():
    """Get default values for new products based on settings"""
    settings = get_product_settings()
    return settings.get_default_values_for_product()


@frappe.whitelist()
def update_product_settings(settings_data):
    """Update Product Settings"""
    settings = get_product_settings()
    settings.update(settings_data)
    settings.save()
    
    return {
        "status": "success",
        "message": "Product settings updated successfully"
    }


@frappe.whitelist()
def apply_settings_to_products(product_filters=None):
    """Apply current settings to existing products"""
    settings = get_product_settings()
    
    # Get products to update
    filters = product_filters or {}
    products = frappe.get_all("Product", filters=filters, fields=["name"])
    
    updated_count = 0
    for product in products:
        try:
            product_doc = frappe.get_doc("Product", product.name)
            
            # Apply default values where current values are empty
            default_values = settings.get_default_values_for_product()
            for field, value in default_values.items():
                if hasattr(product_doc, field) and not product_doc.get(field):
                    product_doc.set(field, value)
            
            product_doc.save()
            updated_count += 1
        except Exception as e:
            frappe.log_error(f"Error updating product {product.name}: {str(e)}")
    
    return {
        "status": "success",
        "message": f"Updated {updated_count} products with current settings",
        "updated_count": updated_count
    }


@frappe.whitelist()
def generate_sku(product_name, product_code=None):
    """Generate SKU based on settings"""
    settings = get_product_settings()
    
    if not settings.auto_generate_sku:
        return product_code or ""
    
    prefix = settings.sku_prefix or "SKU"
    
    # Generate unique SKU
    base_sku = f"{prefix}-{product_code or product_name[:10].upper().replace(' ', '')}"
    sku = base_sku
    counter = 1
    
    while frappe.db.exists("Product", {"sku": sku}):
        sku = f"{base_sku}-{counter:03d}"
        counter += 1
    
    return sku


@frappe.whitelist()
def validate_product_data(product_data):
    """Validate product data against settings"""
    settings = get_product_settings()
    errors = []
    
    # Validate pricing
    if product_data.get("discount_percentage"):
        if flt(product_data["discount_percentage"]) > flt(settings.max_discount_percentage):
            errors.append(f"Discount percentage cannot exceed {settings.max_discount_percentage}%")
    
    # Validate images
    if product_data.get("product_images") and len(product_data["product_images"]) > cint(settings.max_product_images):
        errors.append(f"Cannot add more than {settings.max_product_images} images")
    
    # Validate inventory
    if product_data.get("min_stock_level") and product_data.get("max_stock_level"):
        if cint(product_data["min_stock_level"]) >= cint(product_data["max_stock_level"]):
            errors.append("Minimum stock level must be less than maximum stock level")
    
    return {
        "valid": len(errors) == 0,
        "errors": errors
    }
