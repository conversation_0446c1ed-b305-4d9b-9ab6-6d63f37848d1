# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import flt, cint, nowdate
from frappe.website.utils import clear_cache
import json


class Product(Document):
    def before_insert(self):
        """Actions before inserting new product"""
        self.apply_default_settings()
        self.generate_sku_if_needed()

    def validate(self):
        """Validate the Product document"""
        self.validate_against_settings()
        self.validate_pricing()
        self.validate_inventory()
        self.validate_product_code()
        self.set_meta_defaults()
        self.calculate_discounted_price()

    def apply_default_settings(self):
        """Apply default values from Product Settings"""
        try:
            from ecommerce.ecommerce.doctype.product_settings.product_settings import (
                get_default_product_values,
            )

            defaults = get_default_product_values()

            for field, value in defaults.items():
                if hasattr(self, field) and not self.get(field):
                    self.set(field, value)
        except Exception:
            # If Product Settings doesn't exist or has issues, continue with defaults
            pass

    def generate_sku_if_needed(self):
        """Generate SKU if auto-generation is enabled"""
        if not self.sku:
            try:
                from ecommerce.ecommerce.doctype.product_settings.product_settings import (
                    generate_sku,
                )

                self.sku = generate_sku(self.product_name, self.product_code)
            except Exception:
                # Fallback SKU generation
                self.sku = f"SKU-{self.product_code or self.product_name[:10].upper().replace(' ', '')}"

    def validate_against_settings(self):
        """Validate product data against Product Settings"""
        try:
            from ecommerce.ecommerce.doctype.product_settings.product_settings import (
                validate_product_data,
            )

            product_data = {
                "discount_percentage": self.discount_percentage,
                "product_images": self.get("product_images", []),
                "min_stock_level": self.min_stock_level,
                "max_stock_level": self.max_stock_level,
            }

            validation_result = validate_product_data(product_data)
            if not validation_result["valid"]:
                for error in validation_result["errors"]:
                    frappe.throw(error)
        except ImportError:
            # Product Settings not available, skip validation
            pass

    def validate_pricing(self):
        """Validate pricing information"""
        if flt(self.price) <= 0:
            frappe.throw("Price must be greater than 0")

        if self.compare_price and flt(self.compare_price) < flt(self.price):
            frappe.throw("Compare price cannot be less than selling price")

        if self.cost_price and flt(self.cost_price) > flt(self.price):
            frappe.msgprint(
                "Cost price is higher than selling price. Please review pricing."
            )

    def validate_inventory(self):
        """Validate inventory settings"""
        if self.track_inventory:
            if cint(self.stock_quantity) < 0:
                frappe.throw("Stock quantity cannot be negative")

            if self.min_stock_level and cint(self.min_stock_level) < 0:
                frappe.throw("Minimum stock level cannot be negative")

            if self.max_stock_level and self.min_stock_level:
                if cint(self.max_stock_level) < cint(self.min_stock_level):
                    frappe.throw(
                        "Maximum stock level cannot be less than minimum stock level"
                    )

    def validate_product_code(self):
        """Validate product code uniqueness"""
        if self.product_code:
            existing = frappe.db.exists(
                "Product",
                {"product_code": self.product_code, "name": ["!=", self.name]},
            )
            if existing:
                frappe.throw(f"Product Code '{self.product_code}' already exists")

    def set_meta_defaults(self):
        """Set default meta information if not provided"""
        if not self.meta_title:
            self.meta_title = self.product_name

        if not self.meta_description and self.short_description:
            self.meta_description = self.short_description[:160]

    def calculate_discounted_price(self):
        """Calculate discounted price based on discount percentage"""
        if self.discount_percentage:
            discount_amount = flt(self.price) * flt(self.discount_percentage) / 100
            self.discounted_price = flt(self.price) - discount_amount
        else:
            self.discounted_price = self.price

    def on_update(self):
        """Actions to perform after updating the document"""
        self.sync_with_item()
        if self.show_in_website:
            clear_cache()

    def sync_with_item(self):
        """Sync with ERPNext Item doctype"""
        item_code = self.product_code or self.name

        if not frappe.db.exists("Item", item_code):
            self.create_item()
        else:
            self.update_item()

    def create_item(self):
        """Create ERPNext Item record"""
        item_code = self.product_code or self.name

        item = frappe.get_doc(
            {
                "doctype": "Item",
                "item_code": item_code,
                "item_name": self.product_name,
                "item_group": "Products",
                "stock_uom": "Nos",
                "is_stock_item": 1 if self.track_inventory else 0,
                "include_item_in_manufacturing": 0,
                "is_sales_item": 1,
                "is_purchase_item": 1,
                "description": self.short_description or self.product_name,
                "brand": self.brand,
                "country_of_origin": self.country_of_origin,
                "weight_per_unit": self.weight,
                "warranty_period": self.warranty_period,
            }
        )

        # Add item price
        item.append(
            "item_defaults",
            {
                "company": frappe.defaults.get_user_default("Company"),
                "default_warehouse": frappe.db.get_single_value(
                    "Stock Settings", "default_warehouse"
                ),
            },
        )

        item.insert(ignore_permissions=True)

        # Create Item Price
        self.create_item_price(item_code)

    def update_item(self):
        """Update existing ERPNext Item record"""
        item_code = self.product_code or self.name
        item = frappe.get_doc("Item", item_code)

        item.item_name = self.product_name
        item.description = self.short_description or self.product_name
        item.brand = self.brand
        item.country_of_origin = self.country_of_origin
        item.weight_per_unit = self.weight
        item.warranty_period = self.warranty_period
        item.is_stock_item = 1 if self.track_inventory else 0

        item.save(ignore_permissions=True)

        # Update Item Price
        self.update_item_price(item_code)

    def create_item_price(self, item_code):
        """Create Item Price record"""
        if not frappe.db.exists(
            "Item Price", {"item_code": item_code, "price_list": "Standard Selling"}
        ):
            item_price = frappe.get_doc(
                {
                    "doctype": "Item Price",
                    "item_code": item_code,
                    "price_list": "Standard Selling",
                    "price_list_rate": self.price,
                    "currency": self.currency or "TZS",
                }
            )
            item_price.insert(ignore_permissions=True)

    def update_item_price(self, item_code):
        """Update Item Price record"""
        if frappe.db.exists(
            "Item Price", {"item_code": item_code, "price_list": "Standard Selling"}
        ):
            item_price = frappe.get_doc(
                "Item Price", {"item_code": item_code, "price_list": "Standard Selling"}
            )
            item_price.price_list_rate = self.price
            item_price.currency = self.currency or "TZS"
            item_price.save(ignore_permissions=True)

    def update_stock(self, quantity_change, warehouse=None):
        """Update stock quantity"""
        if self.track_inventory:
            new_quantity = cint(self.stock_quantity) + cint(quantity_change)
            if new_quantity < 0:
                frappe.throw("Insufficient stock available")

            self.stock_quantity = new_quantity
            self.save(ignore_permissions=True)

            # Update stock in ERPNext if item exists
            item_code = self.product_code or self.name
            if frappe.db.exists("Item", item_code):
                self.update_item_stock(item_code, quantity_change, warehouse)

    def update_item_stock(self, item_code, quantity_change, warehouse):
        """Update stock in ERPNext Stock Entry"""
        if not warehouse:
            warehouse = frappe.db.get_single_value(
                "Stock Settings", "default_warehouse"
            )

        if warehouse and quantity_change != 0:
            stock_entry = frappe.get_doc(
                {
                    "doctype": "Stock Entry",
                    "stock_entry_type": (
                        "Material Receipt" if quantity_change > 0 else "Material Issue"
                    ),
                    "company": frappe.defaults.get_user_default("Company"),
                }
            )

            stock_entry.append(
                "items",
                {
                    "item_code": item_code,
                    "qty": abs(quantity_change),
                    "t_warehouse": warehouse if quantity_change > 0 else None,
                    "s_warehouse": warehouse if quantity_change < 0 else None,
                },
            )

            stock_entry.submit()

    def get_stock_status(self):
        """Get stock status"""
        if not self.track_inventory:
            return "In Stock"

        if cint(self.stock_quantity) <= 0:
            return "Out of Stock"
        elif self.min_stock_level and cint(self.stock_quantity) <= cint(
            self.min_stock_level
        ):
            return "Low Stock"
        else:
            return "In Stock"

    def is_in_stock(self, required_quantity=1):
        """Check if product is in stock"""
        if not self.track_inventory:
            return True
        return cint(self.stock_quantity) >= cint(required_quantity)

    def get_final_price(self):
        """Get final price after discount"""
        if hasattr(self, "discounted_price") and self.discounted_price:
            return self.discounted_price
        return self.price

    def get_product_images(self):
        """Get all product images"""
        images = []
        if self.featured_image:
            images.append(self.featured_image)

        for img in self.product_images:
            if img.image and img.image not in images:
                images.append(img.image)

        return images

    def get_related_products(self, limit=4):
        """Get related products from same category"""
        return frappe.get_all(
            "Product",
            filters={
                "category": self.category,
                "name": ["!=", self.name],
                "is_active": 1,
                "show_in_website": 1,
            },
            fields=["name", "product_name", "price", "featured_image"],
            limit=limit,
        )


@frappe.whitelist()
def search_products(query, category=None, min_price=None, max_price=None, limit=20):
    """Search products with filters"""
    filters = {"is_active": 1, "show_in_website": 1}

    if category:
        filters["category"] = category

    if min_price:
        filters["price"] = [">=", flt(min_price)]

    if max_price:
        if "price" in filters:
            filters["price"] = ["between", [flt(min_price), flt(max_price)]]
        else:
            filters["price"] = ["<=", flt(max_price)]

    # Search in product name and description
    or_filters = [
        {"product_name": ["like", f"%{query}%"]},
        {"short_description": ["like", f"%{query}%"]},
        {"tags": ["like", f"%{query}%"]},
    ]

    return frappe.get_all(
        "Product",
        filters=filters,
        or_filters=or_filters,
        fields=["name", "product_name", "price", "featured_image", "short_description"],
        order_by="product_name",
        limit=limit,
    )


@frappe.whitelist()
def get_featured_products(limit=8):
    """Get featured products for homepage"""
    return frappe.get_all(
        "Product",
        filters={"is_featured": 1, "is_active": 1, "show_in_website": 1},
        fields=["name", "product_name", "price", "featured_image", "short_description"],
        order_by="modified desc",
        limit=limit,
    )


@frappe.whitelist()
def get_product_details(product_name):
    """Get detailed product information"""
    product = frappe.get_doc("Product", product_name)

    return {
        "product": product,
        "images": product.get_product_images(),
        "stock_status": product.get_stock_status(),
        "final_price": product.get_final_price(),
        "related_products": product.get_related_products(),
    }
